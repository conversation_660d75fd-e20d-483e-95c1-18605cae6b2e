
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Monitor, Tv, Smartphone } from 'lucide-react';
import { ProductCategory } from '@/types';
import { ProductCard } from './ProductCard';

interface ProductTabsProps {
  products: {category: ProductCategory; type: string; quantity: number}[];
  onAddProduct: (category: ProductCategory) => void;
  onRemoveProduct: (index: number) => void;
  onUpdateProductType: (index: number, type: string) => void;
  onUpdateProductQuantity: (index: number, quantity: number) => void;
}

export const ProductTabs: React.FC<ProductTabsProps> = ({
  products,
  onAddProduct,
  onRemoveProduct,
  onUpdateProductType,
  onUpdateProductQuantity
}) => {
  const getCategoryIcon = (category: ProductCategory) => {
    switch (category) {
      case 'KIP':
        return <Monitor className="h-6 w-6" />;
      case 'TV':
        return <Tv className="h-6 w-6" />;
      case 'Mobile':
        return <Smartphone className="h-6 w-6" />;
      default:
        return <Monitor className="h-6 w-6" />;
    }
  };

  const renderTabContent = (category: ProductCategory, buttonText: string) => {
    const categoryProducts = products.filter(p => p.category === category);
    
    return (
      <TabsContent value={category.toLowerCase()} className="space-y-4">
        {categoryProducts.map((product, categoryIndex) => {
          const globalIndex = products.findIndex(p => p === product);
          return (
            <ProductCard
              key={globalIndex}
              product={product}
              index={globalIndex}
              categoryIndex={categoryIndex}
              onRemove={() => onRemoveProduct(globalIndex)}
              onUpdateType={(type) => onUpdateProductType(globalIndex, type)}
              onUpdateQuantity={(quantity) => onUpdateProductQuantity(globalIndex, quantity)}
            />
          );
        })}
        <Button 
          variant="outline" 
          className="w-full h-16 md:h-20 text-lg font-semibold rounded-2xl border-2 border-red-200 hover:border-red-500 hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback hover-scale"
          onClick={() => onAddProduct(category)}
        >
          <Plus size={20} className="mr-2" />
          {buttonText}
        </Button>
      </TabsContent>
    );
  };

  return (
    <Tabs defaultValue="kip" className="w-full">
      <TabsList className="mobile-tabs mb-4 md:mb-6 w-full glass-card rounded-3xl p-2">
        <TabsTrigger
          value="kip"
          className="mobile-tab text-sm md:text-base font-semibold rounded-2xl data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200 touch-feedback"
          data-primary="true"
        >
          <div className="flex items-center gap-1 md:gap-2">
            {getCategoryIcon('KIP')}
            <span className="hidden sm:inline">KIP</span>
          </div>
        </TabsTrigger>
        <TabsTrigger
          value="tv"
          className="mobile-tab text-sm md:text-base font-semibold rounded-2xl data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200 touch-feedback"
          data-primary="true"
        >
          <div className="flex items-center gap-1 md:gap-2">
            {getCategoryIcon('TV')}
            <span className="hidden sm:inline">TV</span>
          </div>
        </TabsTrigger>
        <TabsTrigger
          value="mobile"
          className="mobile-tab text-sm md:text-base font-semibold rounded-2xl data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200 touch-feedback"
          data-primary="true"
        >
          <div className="flex items-center gap-1 md:gap-2">
            {getCategoryIcon('Mobile')}
            <span className="hidden sm:inline">Mobile</span>
          </div>
        </TabsTrigger>
      </TabsList>

      {renderTabContent('KIP', 'KIP hinzufügen')}
      {renderTabContent('TV', 'TV Option hinzufügen')}
      {renderTabContent('Mobile', 'Mobile Option hinzufügen')}
    </Tabs>
  );
};
