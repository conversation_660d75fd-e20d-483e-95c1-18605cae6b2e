/**
 * Mobile-First Optimization Utilities
 * Provides utilities for ensuring mobile-first design compliance
 */

import { triggerHapticFeedback } from '@/hooks/useSwipeGestures';

// Touch target size constants (following Apple/Google guidelines)
export const TOUCH_TARGET_SIZES = {
  MINIMUM: 44, // Minimum recommended size
  COMFORTABLE: 48, // Comfortable size for most interactions
  LARGE: 56, // Large size for primary actions
  EXTRA_LARGE: 64, // Extra large for critical actions
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
} as const;

/**
 * Validates if an element meets touch target size requirements
 */
export const validateTouchTarget = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return rect.width >= TOUCH_TARGET_SIZES.MINIMUM && rect.height >= TOUCH_TARGET_SIZES.MINIMUM;
};

/**
 * Validates all touch targets on a page
 */
export const validateAllTouchTargets = (container?: HTMLElement): void => {
  const root = container || document.body;
  const interactiveElements = root.querySelectorAll(
    'button, input, select, textarea, [role="button"], [role="tab"], a, [tabindex]:not([tabindex="-1"])'
  );

  const violations: HTMLElement[] = [];

  interactiveElements.forEach((element) => {
    if (!validateTouchTarget(element as HTMLElement)) {
      violations.push(element as HTMLElement);
      console.warn('Touch target too small:', element);
    }
  });

  if (violations.length > 0) {
    console.warn(`Found ${violations.length} touch target violations`);
  }
};

/**
 * Adds mobile-optimized event listeners with haptic feedback
 */
export const addMobileEventListeners = (element: HTMLElement, options: {
  onClick?: () => void;
  hapticType?: 'light' | 'medium' | 'heavy';
  preventDoubleClick?: boolean;
}): void => {
  const { onClick, hapticType = 'light', preventDoubleClick = true } = options;
  
  let lastClickTime = 0;

  const handleClick = (event: Event) => {
    if (preventDoubleClick) {
      const now = Date.now();
      if (now - lastClickTime < 300) {
        event.preventDefault();
        return;
      }
      lastClickTime = now;
    }

    triggerHapticFeedback(hapticType);
    onClick?.();
  };

  element.addEventListener('click', handleClick);
  element.addEventListener('touchstart', () => {
    element.style.transform = 'scale(0.96)';
  });
  element.addEventListener('touchend', () => {
    element.style.transform = '';
  });
};

/**
 * Checks if the current device is mobile
 */
export const isMobileDevice = (): boolean => {
  return window.innerWidth < BREAKPOINTS.MOBILE;
};

/**
 * Checks if the current device is tablet
 */
export const isTabletDevice = (): boolean => {
  return window.innerWidth >= BREAKPOINTS.MOBILE && window.innerWidth < BREAKPOINTS.TABLET;
};

/**
 * Checks if the current device is desktop
 */
export const isDesktopDevice = (): boolean => {
  return window.innerWidth >= BREAKPOINTS.TABLET;
};

/**
 * Gets the appropriate touch target size for the current device
 */
export const getOptimalTouchTargetSize = (): number => {
  if (isMobileDevice()) return TOUCH_TARGET_SIZES.COMFORTABLE;
  if (isTabletDevice()) return TOUCH_TARGET_SIZES.MINIMUM;
  return 40; // Desktop standard
};

/**
 * Applies mobile-first optimizations to a container
 */
export const applyMobileOptimizations = (container: HTMLElement): void => {
  // Add mobile-specific classes
  if (isMobileDevice()) {
    container.classList.add('mobile-optimized');
  }

  // Validate touch targets
  validateAllTouchTargets(container);

  // Add touch feedback to interactive elements
  const interactiveElements = container.querySelectorAll('.touch-feedback');
  interactiveElements.forEach((element) => {
    addMobileEventListeners(element as HTMLElement, {
      hapticType: 'light'
    });
  });
};

/**
 * Optimizes form inputs for mobile
 */
export const optimizeFormForMobile = (form: HTMLFormElement): void => {
  const inputs = form.querySelectorAll('input, select, textarea');
  
  inputs.forEach((input) => {
    const element = input as HTMLInputElement;
    
    // Prevent zoom on iOS
    if (element.type === 'text' || element.type === 'email' || element.type === 'tel') {
      element.style.fontSize = '16px';
    }
    
    // Add mobile-specific attributes
    element.setAttribute('autocomplete', 'on');
    element.setAttribute('autocapitalize', 'words');
    
    // Ensure minimum touch target size
    if (!validateTouchTarget(element)) {
      element.style.minHeight = `${TOUCH_TARGET_SIZES.COMFORTABLE}px`;
    }
  });
};

/**
 * Debounced resize handler for responsive updates
 */
export const createResponsiveHandler = (callback: () => void, delay = 250) => {
  let timeoutId: NodeJS.Timeout;
  
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(callback, delay);
  };
};

/**
 * Sets up global mobile optimizations
 */
export const setupGlobalMobileOptimizations = (): void => {
  // Validate touch targets on page load
  document.addEventListener('DOMContentLoaded', () => {
    validateAllTouchTargets();
  });

  // Re-validate on dynamic content changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            validateAllTouchTargets(node as HTMLElement);
          }
        });
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Handle orientation changes
  window.addEventListener('orientationchange', createResponsiveHandler(() => {
    setTimeout(() => {
      validateAllTouchTargets();
    }, 500); // Wait for orientation change to complete
  }));

  // Handle resize events
  window.addEventListener('resize', createResponsiveHandler(() => {
    validateAllTouchTargets();
  }));
};
