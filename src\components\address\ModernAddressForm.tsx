import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useData } from '@/context/data';
import { HouseType } from '@/types';
import { toast } from 'sonner';
import {
  getCityByPostalCode,
  getPostalCodeSuggestions,
  getStreetSuggestions
} from '@/services/address';
import { MapPin, Building, Home as HomeIcon, Hash, Navigation, Loader2 } from 'lucide-react';
import Address<PERSON><PERSON><PERSON>ield from './AddressFormField';
import ImprovedAutocomplete from './ImprovedAutocomplete';
import EFHVisitTracker from '../visit/EFHVisitTracker';
import { QuickNACard } from '@/components/visit-tracking/QuickNAButton';
import { GeocodingService } from '@/services/geocoding/geocodingService';
import { useAddressPersistence } from '@/hooks/useAddressPersistence';
// Sequential tracking functionality integrated inline for HMR stability

interface ModernAddressFormProps {
  houseType: HouseType;
}

const ModernAddressForm: React.FC<ModernAddressFormProps> = ({ houseType }) => {
  const navigate = useNavigate();
  const { addAddress, addHouse, addVisit, addresses } = useData();
  
  const [zipCode, setZipCode] = useState('');
  const [zipCodeSuggestions, setZipCodeSuggestions] = useState<string[]>([]);
  const [city, setCity] = useState('');
  const [street, setStreet] = useState('');
  const [streetSuggestions, setStreetSuggestions] = useState<string[]>([]);
  const [houseNumber, setHouseNumber] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentVisitId, setCurrentVisitId] = useState<string | null>(null);
  const [isLoadingGPS, setIsLoadingGPS] = useState(false);

  // Sequential tracking state - inline implementation for HMR stability
  const [completedHouses, setCompletedHouses] = useState<string[]>([]);
  const [currentStreet, setCurrentStreet] = useState<string>('');
  const isSequentialTracking = completedHouses.length > 0;

  // Address persistence hook
  const { persistedAddress, saveAddress, clearAddress, isCurrentAddress, hasPersisted } = useAddressPersistence();

  const addCompletedHouse = (houseNumber: string, street: string) => {
    if (street !== currentStreet) {
      setCompletedHouses([houseNumber]);
      setCurrentStreet(street);
    } else {
      setCompletedHouses(prev => [...prev, houseNumber]);
    }
  };

  const resetTracking = () => {
    setCompletedHouses([]);
    setCurrentStreet('');
  };

  // Validation states
  const isZipCodeValid = zipCode.length === 5 && /^\d{5}$/.test(zipCode);
  const isCityValid = city.length > 2;
  const isStreetValid = street.length > 2;
  const isHouseNumberValid = houseNumber.length > 0;
  const isFormValid = isZipCodeValid && isCityValid && isStreetValid && isHouseNumberValid;

  // Update city when zip code changes
  useEffect(() => {
    if (zipCode.length === 5) {
      const matchedCity = getCityByPostalCode(zipCode);
      if (matchedCity) {
        setCity(matchedCity);
      }
    }
  }, [zipCode]);

  // Update street suggestions when city changes
  useEffect(() => {
    if (city) {
      setStreetSuggestions(getStreetSuggestions(city, street));
    }
  }, [city, street]);

  // Update zip code suggestions when typing
  useEffect(() => {
    if (zipCode.length >= 2) {
      setZipCodeSuggestions(getPostalCodeSuggestions(zipCode));
    } else {
      setZipCodeSuggestions([]);
    }
  }, [zipCode]);

  // Load persisted address on component mount
  useEffect(() => {
    if (persistedAddress && !zipCode && !city && !street) {
      setZipCode(persistedAddress.zipCode);
      setCity(persistedAddress.city);
      setStreet(persistedAddress.street);
    }
  }, [persistedAddress, zipCode, city, street]);

  const handleZipCodeChange = (value: string) => {
    // Only allow numbers and limit to 5 digits
    const cleanValue = value.replace(/\D/g, '').slice(0, 5);
    setZipCode(cleanValue);
    if (cleanValue.length !== 5) {
      setCity('');
    }
  };

  const handleHouseNumberChange = (value: string) => {
    // Allow numbers, letters, and common separators
    const cleanValue = value.replace(/[^0-9a-zA-Z\-\/\s]/g, '').slice(0, 10);
    setHouseNumber(cleanValue);
  };

  const validateAndShowError = () => {
    if (!isZipCodeValid) {
      toast.error('Bitte geben Sie eine gültige 5-stellige Postleitzahl ein');
      return false;
    }
    if (!isCityValid) {
      toast.error('Bitte geben Sie eine gültige Stadt ein');
      return false;
    }
    if (!isStreetValid) {
      toast.error('Bitte geben Sie eine gültige Straße ein');
      return false;
    }
    if (!isHouseNumberValid) {
      toast.error('Bitte geben Sie eine Hausnummer ein');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateAndShowError()) return;
    
    setIsSubmitting(true);
    
    try {
      // Check if address already exists
      let addressId: string;
      const existingAddress = addresses.find(
        addr => addr.zipCode === zipCode && 
               addr.city.toLowerCase() === city.toLowerCase() && 
               addr.street.toLowerCase() === street.toLowerCase()
      );
      
      if (existingAddress) {
        addressId = existingAddress.id;
        toast.success('Adresse bereits vorhanden - weiter zur Besuchserfassung');
      } else {
        // Create new address
        const newAddress = addAddress({
          zipCode,
          city,
          street
        });
        addressId = newAddress.id;
        toast.success('Neue Adresse erfolgreich hinzugefügt');
      }
      
      // Create house
      const house = addHouse({
        addressId,
        houseNumber,
        type: houseType,
        // Mock coordinates - in real app would get from geocoding
        latitude: 48.1351 + (Math.random() * 0.01),
        longitude: 11.5820 + (Math.random() * 0.01),
      });
      
      // Save address for persistence
      saveAddress(zipCode, city, street);

      if (houseType === 'EFH') {
        // For EFH: Create visit immediately and show tracker
        const visit = addVisit({
          houseId: house.id,
          timestamp: new Date().toISOString(),
          status: 'N/A'
        });
        setCurrentVisitId(visit.id);
        toast.success('EFH-Besuch erstellt - Status erfassen');
      } else {
        // For MFH: Navigate to MFH manager
        navigate(`/mfh/${house.id}`);
      }
    } catch (error) {
      toast.error('Fehler beim Speichern der Adresse');
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNewEntry = () => {
    setCurrentVisitId(null);
    setZipCode('');
    setCity('');
    setStreet('');
    setHouseNumber('');
    resetTracking(); // Reset sequential tracking
    toast.success('Bereit für neue Adresseingabe');
  };

  // GPS-basierte automatische Adresseingabe
  const handleGPSAddressCapture = async () => {
    setIsLoadingGPS(true);

    try {
      // GPS-Position abrufen mit hoher Genauigkeit
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        if (!navigator.geolocation) {
          reject(new Error('Geolocation wird von diesem Browser nicht unterstützt'));
          return;
        }

        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 0
          }
        );
      });

      const { latitude, longitude } = position.coords;

      // Detailliertes Reverse Geocoding für strukturierte Adressdaten
      // Note: In production, you would use a real Mapbox access token
      const geocodingService = new GeocodingService(process.env.VITE_MAPBOX_ACCESS_TOKEN || 'demo-token');
      const addressData = await geocodingService.reverseGeocodeDetailed(longitude, latitude);

      if (!addressData) {
        throw new Error('Keine Adresse für diese Position gefunden');
      }

      // Strukturierte Adressdaten verwenden
      if (addressData.zipCode && addressData.city && addressData.street) {
        setZipCode(addressData.zipCode);
        setCity(addressData.city);
        setStreet(addressData.street);

        // Hausnummer nur setzen, wenn verfügbar
        if (addressData.houseNumber) {
          setHouseNumber(addressData.houseNumber);
          toast.success(`📍 GPS-Adresse erfolgreich erfasst: ${addressData.street} ${addressData.houseNumber}, ${addressData.zipCode} ${addressData.city}`);
        } else {
          setHouseNumber('');
          toast.success(`📍 GPS-Adresse teilweise erfasst: ${addressData.street}, ${addressData.zipCode} ${addressData.city}. Bitte Hausnummer eingeben.`);

          // Fokus auf Hausnummer-Feld setzen
          setTimeout(() => {
            const houseNumberInput = document.getElementById('houseNumber');
            if (houseNumberInput) {
              houseNumberInput.focus();
              houseNumberInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }, 500);
        }
      } else {
        // Fallback: Versuche Adresse zu parsen
        const fallbackParse = (fullAddress: string) => {
          const parts = fullAddress.split(',').map(part => part.trim());

          if (parts.length >= 2) {
            const streetPart = parts[0];
            const streetMatch = streetPart.match(/^(.+?)\s+(\d+[a-zA-Z]?)$/);

            const cityPart = parts[1];
            const cityMatch = cityPart.match(/^(\d{5})\s+(.+)$/);

            if (streetMatch && cityMatch) {
              return {
                street: streetMatch[1].trim(),
                houseNumber: streetMatch[2].trim(),
                zipCode: cityMatch[1].trim(),
                city: cityMatch[2].trim()
              };
            }
          }
          return null;
        };

        const fallbackData = addressData.fullAddress ? fallbackParse(addressData.fullAddress) : null;

        if (fallbackData) {
          setZipCode(fallbackData.zipCode);
          setCity(fallbackData.city);
          setStreet(fallbackData.street);
          setHouseNumber(fallbackData.houseNumber);
          toast.success(`📍 GPS-Adresse erfolgreich erfasst: ${fallbackData.street} ${fallbackData.houseNumber}`);
        } else {
          toast.warning(`GPS-Position erfasst, aber Adresse konnte nicht automatisch erkannt werden. Bitte manuell eingeben.`);
          console.log('GPS Koordinaten:', { latitude, longitude, addressData });
        }
      }

    } catch (error) {
      console.error('GPS-Fehler:', error);

      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            toast.error('GPS-Berechtigung verweigert. Bitte in den Browser-Einstellungen aktivieren.');
            break;
          case error.POSITION_UNAVAILABLE:
            toast.error('GPS-Position nicht verfügbar. Bitte Standortdienste aktivieren.');
            break;
          case error.TIMEOUT:
            toast.error('GPS-Timeout. Bitte erneut versuchen.');
            break;
          default:
            toast.error('GPS-Fehler aufgetreten.');
        }
      } else {
        toast.error('Fehler beim Abrufen der GPS-Adresse.');
      }
    } finally {
      setIsLoadingGPS(false);
    }
  };

  // Intelligenter Reset für sequenzielles Tracking
  const handleQuickNASuccess = () => {
    // Tracking-Daten aktualisieren
    addCompletedHouse(houseNumber, street);

    // Behalte PLZ, Stadt und Straße bei - nur Hausnummer zurücksetzen
    setHouseNumber('');

    // Fokus auf Hausnummer-Feld setzen
    setTimeout(() => {
      const houseNumberInput = document.getElementById('houseNumber');
      if (houseNumberInput) {
        houseNumberInput.focus();
        houseNumberInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  };

  // Show EFH Visit Tracker if visit was created
  if (currentVisitId && houseType === 'EFH') {
    return (
      <div className="space-y-0 w-full">
        <EFHVisitTracker visitId={currentVisitId} />
        <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-t border-blue-100">
          <button
            onClick={handleNewEntry}
            className="w-full h-16 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold text-lg rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center gap-3"
          >
            <span className="text-2xl">🏠</span>
            <span>Neue Adresse eingeben</span>
            <span className="text-xl">→</span>
          </button>
          <p className="text-center text-sm text-blue-600 mt-2 font-medium">
            Bereit für das nächste Haus
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Floating Progress Indicator - inline implementation for HMR stability */}
      {isSequentialTracking && (
        <div className="fixed top-20 right-4 z-40 transition-all duration-300">
          <div className="bg-green-500 text-white px-3 py-2 rounded-full shadow-lg flex items-center gap-2">
            <span className="text-sm font-semibold">{completedHouses.length}</span>
          </div>
        </div>
      )}

      <Card className="w-full bg-white/95 backdrop-blur-sm border-0 shadow-none rounded-none overflow-hidden animate-fade-in m-0">
        <CardHeader className="text-center pb-6 pt-8 bg-gradient-to-b from-red-50/50 to-transparent m-0">
        <div className="flex items-center justify-center mb-4">
          {houseType === 'EFH' ? (
            <HomeIcon className="h-8 w-8 text-red-600" />
          ) : (
            <Building className="h-8 w-8 text-red-600" />
          )}
        </div>
        <CardTitle className="text-2xl font-bold text-gray-800">
          {houseType === 'EFH' ? 'Einfamilienhaus' : 'Mehrfamilienhaus'}
        </CardTitle>
        <p className="text-gray-600 mt-2">Adresse für den Besuch eingeben</p>
      </CardHeader>
      
      <CardContent className="px-8 pt-6">
        {/* Sequential Tracking Indicator - inline implementation for HMR stability */}
        {isSequentialTracking && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <MapPin className="h-5 w-5 text-green-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-green-800">Sequenzielles Tracking</h3>
                  <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">
                    {completedHouses.length} Häuser erfasst
                  </span>
                </div>
                <p className="text-sm text-green-600">
                  📍 {street}, {city}
                </p>
                <div className="flex items-center gap-4 mt-2">
                  <span className="text-xs text-green-600">
                    ✅ Erfasst: {completedHouses.join(', ')}
                  </span>
                  <span className="text-xs text-blue-600">
                    ⏰ Nächstes Haus bereit
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Quick Action Buttons */}
          <div className="space-y-3">
            {/* GPS-Adresse Button */}
            <Button
              type="button"
              onClick={handleGPSAddressCapture}
              disabled={isLoadingGPS}
              className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
              {isLoadingGPS ? (
                <div className="flex items-center">
                  <Loader2 className="w-5 h-5 animate-spin mr-3" />
                  GPS-Position wird ermittelt...
                </div>
              ) : (
                <div className="flex items-center">
                  <Navigation className="w-5 h-5 mr-3" />
                  📍 GPS-Adresse automatisch erfassen
                </div>
              )}
            </Button>

            {/* Gespeicherte Adresse Button */}
            {hasPersisted && !isCurrentAddress(zipCode, city, street) && (
              <div className="flex gap-2">
                <Button
                  type="button"
                  onClick={() => {
                    if (persistedAddress) {
                      setZipCode(persistedAddress.zipCode);
                      setCity(persistedAddress.city);
                      setStreet(persistedAddress.street);
                      setHouseNumber('');
                      toast.success(`Letzte Adresse geladen: ${persistedAddress.street}, ${persistedAddress.city}`);
                    }
                  }}
                  variant="outline"
                  className="flex-1 h-10 text-sm font-medium border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400"
                >
                  <MapPin className="w-4 h-4 mr-2" />
                  Letzte Adresse: {persistedAddress?.street}
                </Button>
                <Button
                  type="button"
                  onClick={() => {
                    clearAddress();
                    toast.success('Gespeicherte Adresse gelöscht');
                  }}
                  variant="outline"
                  size="sm"
                  className="h-10 px-3 text-gray-500 hover:text-red-600 hover:border-red-300"
                >
                  ✕
                </Button>
              </div>
            )}

            <p className="text-xs text-gray-500 text-center">
              {hasPersisted ?
                'GPS für neue Adresse oder letzte Adresse wiederverwenden' :
                'Nutzt hochgenaue GPS-Positionierung für automatische Adresseingabe'
              }
            </p>
          </div>

          <div className="mobile-form-group space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <ImprovedAutocomplete
                id="zipCode"
                label="Postleitzahl"
                value={zipCode}
              onChange={handleZipCodeChange}
              suggestions={zipCodeSuggestions}
              placeholder="12345"
              required
              icon={MapPin}
              isValid={isZipCodeValid}
              maxSuggestions={8}
            />
            
            <AddressFormField
              id="city"
              label="Stadt"
              value={city}
              onChange={setCity}
              placeholder="Stadtname"
              required
              icon={Building}
              isValid={isCityValid}
              disabled={!isZipCodeValid}
              className={!isZipCodeValid ? "bg-gray-50" : ""}
            />
            </div>

            <ImprovedAutocomplete
              id="street"
              label="Straße"
              value={street}
              onChange={setStreet}
              suggestions={streetSuggestions}
              placeholder="Straßenname"
              required
              icon={MapPin}
              isValid={isStreetValid}
              maxSuggestions={12}
            />

            <AddressFormField
              id="houseNumber"
              label="Hausnummer"
              value={houseNumber}
              onChange={handleHouseNumberChange}
              placeholder="123a"
              required
              icon={Hash}
              isValid={isHouseNumberValid}
              maxLength={10}
            />
          </div>
          
          {/* Quick N/A Option for EFH */}
          {houseType === 'EFH' && isFormValid && (
            <QuickNACard
              addressData={{
                zipCode,
                city,
                street,
                houseNumber,
                houseType,
                latitude: 48.1351 + (Math.random() * 0.01),
                longitude: 11.5820 + (Math.random() * 0.01),
              }}
              onSuccess={handleQuickNASuccess}
              isVisible={true}
            />
          )}

          <CardFooter className="px-0 pt-6">
            <Button
              type="submit"
              className="mobile-action-button w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold rounded-xl transition-all duration-200 active:scale-[0.98] shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed touch-feedback"
              disabled={isSubmitting || !isFormValid}
              data-size="large"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                  Speichern...
                </div>
              ) : (
                `${houseType === 'EFH' ? 'Besuch erstellen' : 'Wohnungen verwalten'}`
              )}
            </Button>
          </CardFooter>
        </form>
      </CardContent>
    </Card>
    </>
  );
};

export default ModernAddressForm;
