import React from 'react';
import { VisitStatus } from '@/types';

interface SimpleStatusButtonsProps {
  currentStatus: VisitStatus;
  onStatusUpdate: (status: VisitStatus) => void;
  isUpdating: boolean;
}

export const SimpleStatusButtons: React.FC<SimpleStatusButtonsProps> = ({
  currentStatus,
  onStatusUpdate,
  isUpdating
}) => {
  const statusOptions = [
    {
      status: 'Angetroffen → Sale' as VisitStatus,
      label: '💰 Verkauf!',
      bgColor: 'bg-green-600 hover:bg-green-700',
    },
    {
      status: 'Angetroffen → Termin' as VisitStatus,
      label: '📅 Termin vereinbaren',
      bgColor: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      status: 'Angetroffen → Kein Interesse' as VisitStatus,
      label: '❌ Kein Interesse',
      bgColor: 'bg-neutral-500 hover:bg-neutral-600',
    },
    {
      status: 'N/A' as VisitStatus,
      label: '❓ Nicht angetroffen',
      bgColor: 'bg-red-600 hover:bg-red-700',
    },
  ];

  return (
    <div style={{ padding: '16px' }}>
      <h3 style={{ 
        fontSize: '20px', 
        fontWeight: 'bold', 
        textAlign: 'center', 
        marginBottom: '24px',
        color: '#1f2937'
      }}>
        Was ist passiert?
      </h3>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {statusOptions.map((option) => (
          <button
            key={option.status}
            onClick={() => onStatusUpdate(option.status)}
            disabled={isUpdating || currentStatus === option.status}
            style={{
              width: '100%',
              height: '64px',
              borderRadius: '12px',
              fontSize: '18px',
              fontWeight: '600',
              color: 'white',
              border: 'none',
              cursor: isUpdating || currentStatus === option.status ? 'not-allowed' : 'pointer',
              opacity: isUpdating || currentStatus === option.status ? 0.5 : 1,
              transition: 'all 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
            className={option.bgColor}
            onMouseDown={(e) => {
              e.currentTarget.style.transform = 'scale(0.95)';
            }}
            onMouseUp={(e) => {
              e.currentTarget.style.transform = 'scale(1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1)';
            }}
          >
            {isUpdating ? '⏳ Wird gespeichert...' : option.label}
          </button>
        ))}
      </div>

      {currentStatus !== 'N/A' && (
        <div style={{
          marginTop: '16px',
          textAlign: 'center',
          fontSize: '14px',
          color: '#059669',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '4px'
        }}>
          <span>✅</span>
          <span>Aktueller Status: {currentStatus}</span>
        </div>
      )}
    </div>
  );
};
