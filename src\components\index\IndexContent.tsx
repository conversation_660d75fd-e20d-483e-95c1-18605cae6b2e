
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ModernAddressForm from '@/components/address/ModernAddressForm';
import { Home, Building } from 'lucide-react';
import { useSwipe } from '@/hooks/use-swipe';
import { useSidebar } from '@/components/ui/sidebar';

const IndexContent = () => {
  const { setOpenMobile } = useSidebar();

  // Swipe-Handler für Sidebar öffnen
  useSwipe({
    onSwipeRight: () => {
      setOpenMobile(true);
    }
  });

  return (
    <div className="min-h-full w-full p-0 bg-gradient-to-br from-red-50 via-white to-red-50">
      <div className="w-full h-full p-0">
        <Tabs defaultValue="efh" className="w-full h-full">
          {/* Beautiful Switch/Toggle */}
          <div className="relative bg-gradient-to-r from-red-500 via-red-600 to-red-500 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 via-transparent to-red-400/20"></div>
            <TabsList className="relative grid grid-cols-2 mb-0 h-20 bg-transparent rounded-none border-0 m-0 p-2 gap-2">
              <TabsTrigger
                value="efh"
                className="group relative text-lg py-4 px-8 rounded-2xl font-bold transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] data-[state=active]:bg-white data-[state=active]:text-red-600 data-[state=active]:shadow-2xl data-[state=inactive]:text-white/90 data-[state=inactive]:hover:text-white data-[state=inactive]:hover:bg-white/10 backdrop-blur-sm"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-xl bg-white/20 group-data-[state=active]:bg-red-500 shadow-lg transition-all duration-300">
                    <Home className="h-6 w-6 text-white group-data-[state=active]:text-white" />
                  </div>
                  <div className="text-left">
                    <div className="text-xl font-bold">EFH</div>
                    <div className="text-sm opacity-80 font-medium">Einfamilienhaus</div>
                  </div>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="mfh"
                className="group relative text-lg py-4 px-8 rounded-2xl font-bold transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] data-[state=active]:bg-white data-[state=active]:text-red-600 data-[state=active]:shadow-2xl data-[state=inactive]:text-white/90 data-[state=inactive]:hover:text-white data-[state=inactive]:hover:bg-white/10 backdrop-blur-sm"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-xl bg-white/20 group-data-[state=active]:bg-red-500 shadow-lg transition-all duration-300">
                    <Building className="h-6 w-6 text-white group-data-[state=active]:text-white" />
                  </div>
                  <div className="text-left">
                    <div className="text-xl font-bold">MFH</div>
                    <div className="text-sm opacity-80 font-medium">Mehrfamilienhaus</div>
                  </div>
                </div>
              </TabsTrigger>
            </TabsList>

            {/* Decorative elements */}
            <div className="absolute top-2 left-4 w-2 h-2 bg-white/30 rounded-full"></div>
            <div className="absolute top-4 right-6 w-1 h-1 bg-white/40 rounded-full"></div>
            <div className="absolute bottom-3 left-8 w-1.5 h-1.5 bg-white/25 rounded-full"></div>
          </div>

          <TabsContent value="efh" className="p-0 m-0 animate-fade-in">
            <ModernAddressForm houseType="EFH" />
          </TabsContent>

          <TabsContent value="mfh" className="p-0 m-0 animate-fade-in">
            <ModernAddressForm houseType="MFH" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default IndexContent;
