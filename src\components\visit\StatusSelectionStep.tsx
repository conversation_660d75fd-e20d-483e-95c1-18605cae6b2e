
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { But<PERSON> } from '@/design-system/components/Button';
import { Card, CardContent, AddressCard } from '@/design-system/components/Card';
import { Badge } from '@/components/ui/badge';
import { StatusButtons } from './StatusButtons';
import { SimpleStatusButtons } from './SimpleStatusButtons';
import { Clock, Calendar, Home } from 'lucide-react';
import { Address, House, Visit, Door, VisitStatus } from '@/types';
import { animations } from '@/design-system/animations';
import { cn } from '@/lib/utils';

interface StatusSelectionStepProps {
  visitId: string;
  visit: Visit;
  address: Address;
  house: House;
  existingDoors: Door[];
  onStatusUpdate: (status: VisitStatus) => void;
  isUpdating: boolean;
}

export const StatusSelectionStep: React.FC<StatusSelectionStepProps> = ({
  visitId,
  visit,
  address,
  house,
  existingDoors,
  onStatusUpdate,
  isUpdating
}) => {
  const navigate = useNavigate();

  const getStatusColor = (status: VisitStatus) => {
    switch (status) {
      case 'Angetroffen → Termin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Angetroffen → Kein Interesse':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Angetroffen → Sale':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  return (
    <div className="space-y-6 p-4">
      <AddressCard
        street={address.street}
        houseNumber={house.houseNumber}
        city={address.city}
        zipCode={address.zipCode}
        houseType={house.type}
        step={2}
        totalSteps={2}
      />

      <Card variant="glass" className="overflow-hidden">
        <CardContent>
          {/* Current Status */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-semibold text-gray-700">Aktueller Status:</span>
              <Badge className={getStatusColor(visit.status)} variant="outline">
                {visit.status}
              </Badge>
            </div>

            {/* Show appointment info if exists */}
            {visit.status === 'Angetroffen → Termin' && visit.appointmentDate && visit.appointmentTime && (
              <div className="mb-3 p-3 bg-blue-50/60 rounded-xl border border-blue-200">
                <div className="flex items-center gap-2 text-sm text-blue-800">
                  <Calendar className="h-4 w-4" />
                  <span className="font-medium">
                    Termin: {format(new Date(visit.appointmentDate), 'dd.MM.yyyy')} um {visit.appointmentTime}
                  </span>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              <span>{new Date(visit.timestamp).toLocaleString('de-DE')}</span>
            </div>
          </div>

          {/* Status Update Buttons */}
          <SimpleStatusButtons
            currentStatus={visit.status}
            onStatusUpdate={onStatusUpdate}
            isUpdating={isUpdating}
          />

          {/* Navigation Buttons */}
          <div className="mt-8 space-y-4">
            <Button
              onClick={() => navigate('/')}
              variant="outline"
              size="lg"
              fullWidth
              leftIcon={<Home className="h-4 w-4" />}
            >
              Zur Startseite
            </Button>

            {visit.status === 'Angetroffen → Sale' && (
              <Button
                onClick={() => navigate(`/products/${visitId}`)}
                variant="success"
                size="lg"
                fullWidth
                className={animations.classes.hoverScale}
              >
                Produkte erfassen
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
